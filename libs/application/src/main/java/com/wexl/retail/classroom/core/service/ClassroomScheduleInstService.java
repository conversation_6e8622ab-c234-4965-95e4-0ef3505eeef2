package com.wexl.retail.classroom.core.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;
import static com.wexl.retail.task.domain.TaskStatus.*;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.classroom.core.dto.*;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleInstRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleRepository;
import com.wexl.retail.classroom.core.repository.ScheduleInstAttendanceRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.dto.StudentScheduleResponse;
import com.wexl.retail.task.dto.TaskRequest;
import com.wexl.retail.task.dto.TestRequest;
import com.wexl.retail.task.service.TaskService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClassroomScheduleInstService {

  private final ClassroomScheduleRepository classroomScheduleRepository;
  private final ClassroomScheduleInstRepository classroomScheduleInstRepository;

  private final DateTimeUtil dateTimeUtil;

  private final AuthService authService;

  private final TaskService taskService;

  private final MlpService mlpService;

  private final UserService userService;

  private final ContentService contentService;
  private final ScheduleInstAttendanceService scheduleInstAttendanceService;
  private final ClassroomScheduleService classroomScheduleService;
  private final ScheduleInstAttendanceRepository scheduleInstAttendanceRepository;
  private final ValidationUtils validationUtils;

  public List<ClassroomScheduleInstResponse> getClassroomSchedules(
      User user, Long fromDateInEpoch, Long toDateInEpoch, String orgSlug) {

    var fromdate = dateTimeUtil.convertEpochToIso8601(fromDateInEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(toDateInEpoch);

    if (!AuthUtil.isOrgAdmin(user)) {
      return getTeacherSheduleInsts(user, orgSlug, fromdate, toDate);
    }
    return getScheduleinsts(orgSlug, fromdate, toDate);
  }

  private List<ClassroomScheduleInstResponse> getScheduleinsts(
      String orgSlug, LocalDateTime fromDate, LocalDateTime toDate) {
    var tDate = toDate.with(LocalTime.MAX);
    var fDate = fromDate.with(LocalTime.MIN);
    var classroomSchedulesInsts =
        classroomScheduleInstRepository.findByOrgSlugAndDeletedAtIsNullAndStartTimeBetween(
            orgSlug, fDate, tDate);

    return sortedList(buildScheduleInstResponses(classroomSchedulesInsts));
  }

  public List<ClassroomScheduleInstResponse> buildScheduleInstResponses(
      List<ClassroomScheduleInst> classroomScheduleInsts) {

    return classroomScheduleInsts.stream()
        .map(
            scheduleInst -> {
              var meetingRoom =
                  Objects.nonNull(scheduleInst.getMeetingRoom())
                      ? scheduleInst.getMeetingRoom()
                      : scheduleInst.getClassroomSchedule().getMeetingRoom();
              return ClassroomScheduleInstResponse.builder()
                  .classroomId(scheduleInst.getClassroomSchedule().getClassroom().getId())
                  .classroomName(scheduleInst.getClassroomSchedule().getClassroom().getName())
                  .scheduleId(scheduleInst.getClassroomSchedule().getId())
                  .scheduleInstId(scheduleInst.getId())
                  .title(scheduleInst.getTitle())
                  .studentsCount(
                      getActiveStudents(scheduleInst.getClassroomSchedule().getClassroom()).size())
                  .startTime(convertIso8601ToEpoch(scheduleInst.getStartTime()))
                  .endTime(convertIso8601ToEpoch(scheduleInst.getEndTime()))
                  .meetingRoomId(Objects.nonNull(meetingRoom) ? meetingRoom.getId() : null)
                  .meetingRoomName(Objects.nonNull(meetingRoom) ? meetingRoom.getName() : null)
                  .startDate(
                      convertIso8601ToEpoch(scheduleInst.getClassroomSchedule().getStartDate()))
                  .expiryDate(
                      convertIso8601ToEpoch(scheduleInst.getClassroomSchedule().getEndDate()))
                  .createdDate(
                      convertIso8601ToEpoch(
                          scheduleInst.getClassroomSchedule().getCreatedAt().toLocalDateTime()))
                  .dayOfWeek(scheduleInst.getDayOfWeek().name())
                  .link(Objects.nonNull(meetingRoom) ? meetingRoom.getHostLink() : null)
                  .taskCount(
                      Objects.nonNull(scheduleInst.getTasks())
                          ? scheduleInst.getTasks().size()
                          : null)
                  .status(scheduleInst.getStatus().name())
                  .organization(scheduleInst.getClassroomSchedule().getOrganization().getName())
                  .isAttendanceMarked(isAttendanceMarked(scheduleInst.getId()))
                  .teacherNames(
                      scheduleInst.getClassroomSchedule().getClassroom().getTeachers().stream()
                          .map(teacher -> teacher.getUserInfo().getFirstName())
                          .toList())
                  .build();
            })
        .toList();
  }

  private List<Student> getActiveStudents(Classroom classroom) {
    if (classroom.getStudents().isEmpty()) {
      return new ArrayList<>();
    }
    return new ArrayList<>(
        classroom.getStudents().stream()
            .filter(student -> student.getDeletedAt() == null)
            .toList());
  }

  public List<ClassroomScheduleInstResponse> sortedList(
      List<ClassroomScheduleInstResponse> classroomScheduleInstResponses) {
    List<ClassroomScheduleInstResponse> filteredSortedList = new ArrayList<>();
    var filteredANDSortedToCompletedStatus =
        classroomScheduleInstResponses.stream()
            .filter(response -> response.getStatus().equals(COMPLETED.name()))
            .sorted(Comparator.comparing(ClassroomScheduleInstResponse::getStartTime))
            .toList();
    var filteredANDSortedToNotStartedStatus =
        classroomScheduleInstResponses.stream()
            .filter(response -> response.getStatus().equals(NOT_STARTED.name()))
            .sorted(Comparator.comparing(ClassroomScheduleInstResponse::getStartTime))
            .toList();
    var filteredAndSortedToClosedStatus =
        classroomScheduleInstResponses.stream()
            .filter(response -> response.getStatus().equals(CLOSED.name()))
            .sorted(Comparator.comparing(ClassroomScheduleInstResponse::getStartTime))
            .toList();
    filteredSortedList.addAll(filteredANDSortedToCompletedStatus);
    filteredSortedList.addAll(filteredANDSortedToNotStartedStatus);
    filteredSortedList.addAll(filteredAndSortedToClosedStatus);
    return filteredSortedList;
  }

  public List<String> buildTeacherNames(List<Teacher> teachers) {
    return teachers.stream()
        .map(teacher -> userService.getNameByUserInfo(teacher.getUserInfo()))
        .toList();
  }

  private List<ClassroomScheduleInstResponse> getTeacherSheduleInsts(
      User user, String orgSlug, LocalDateTime fromDate, LocalDateTime toDate) {

    var data =
        classroomScheduleRepository.getTeacherScheduleInsts(
            orgSlug,
            user.getTeacherInfo().getId(),
            fromDate.toLocalDate().toString(),
            toDate.toLocalDate().toString());

    return data.stream()
        .map(
            result -> {
              var classRoom = validationUtils.isClassroomValid(result.getClassroomId());
              return ClassroomScheduleInstResponse.builder()
                  .classroomId(result.getClassroomId())
                  .classroomName(result.getClassroomName())
                  .scheduleId(result.getScheduleId())
                  .scheduleInstId(result.getScheduleInstId())
                  .dayOfWeek(result.getDayOfWeek())
                  .startDate(convertIso8601ToEpoch(result.getStartDate()))
                  .expiryDate(convertIso8601ToEpoch(result.getExpiryDate()))
                  .meetingRoomId(result.getMeetingRoomId())
                  .meetingRoomName(result.getMeetingRoomName())
                  .title(result.getTitle())
                  .status(result.getStatus())
                  .createdDate(convertIso8601ToEpoch(result.getCreatedAt()))
                  .startTime(convertIso8601ToEpoch(result.getStartTime()))
                  .endTime(convertIso8601ToEpoch(result.getEndTime()))
                  .link(result.getLink())
                  .studentsCount(result.getStudentCount())
                  .isAttendanceMarked(isAttendanceMarked(result.getScheduleInstId()))
                  .teacherNames(
                      classRoom.getTeachers().stream()
                          .map(teacher -> teacher.getUserInfo().getFirstName())
                          .toList())
                  .build();
            })
        .sorted(
            Comparator.comparing(ClassroomScheduleInstResponse::getStatus)
                .reversed()
                .thenComparing(ClassroomScheduleInstResponse::getStartTime)
                .reversed())
        .toList();
  }

  private boolean isAttendanceMarked(Long scheduleInstId) {
    return scheduleInstAttendanceRepository
        .findByClassroomScheduleInstId(scheduleInstId)
        .isPresent();
  }

  public void meetingCompletionStatus(
      Long scheduleInstId,
      String orgSlug,
      ClassroomScheduleInstRequest classroomScheduleInstRequest) {
    var optionalScheduleInst = classroomScheduleInstRepository.findById(scheduleInstId);
    if (optionalScheduleInst.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassroomScheduleValidity.Org",
          new String[] {orgSlug});
    }
    var scheduleInst = optionalScheduleInst.get();
    if (Objects.nonNull(classroomScheduleInstRequest.getHostJoinTime())) {
      scheduleInst.setHostJoinTime(
          dateTimeUtil.convertEpochToOffsetDateTime(
              classroomScheduleInstRequest.getHostJoinTime()));
      scheduleInst.setTutors(
          ClassRoomDto.Tutors.builder()
              .tutorIds(List.of(authService.getTeacherDetails().getId()))
              .build());
      classroomScheduleInstRepository.save(scheduleInst);
      return;
    }
    scheduleInst.setTitle(classroomScheduleInstRequest.getTitle());
    scheduleInst.setStatus(classroomScheduleInstRequest.getStatus());
    if (classroomScheduleInstRequest.getStatus().equals(ClassroomMeetingStatus.COMPLETED)) {
      scheduleInstAttendanceService.markAttendanceByCompletingClassroom(scheduleInst);
      scheduleInst
          .getTasks()
          .forEach(
              task ->
                  task.getTaskInsts()
                      .forEach(inst -> updateTaskStatus(inst, ClassroomMeetingStatus.COMPLETED)));
    } else {
      scheduleInst
          .getTasks()
          .forEach(
              task ->
                  task.getTaskInsts()
                      .forEach(inst -> updateTaskStatus(inst, ClassroomMeetingStatus.NOT_STARTED)));
    }
    classroomScheduleInstRepository.save(scheduleInst);
  }

  public void updateTaskStatus(TaskInst taskInst, ClassroomMeetingStatus status) {
    if (taskInst.getCompletionStatus().equals(DRAFT)
        && taskInst.getTask().getTaskType().equals(TaskType.REVISION)
        && ClassroomMeetingStatus.COMPLETED.equals(status)) {
      taskInst.setCompletionStatus(COMPLETED);
    } else if (taskInst.getCompletionStatus().equals(DRAFT)
        && ClassroomMeetingStatus.COMPLETED.equals(status)) {
      taskInst.setCompletionStatus(NOT_STARTED);
    } else if (ClassroomMeetingStatus.COMPLETED.equals(status)
        && TaskType.REVISION.name().equals(taskInst.getTask().getTaskType().name())) {
      taskInst.setCompletionStatus(COMPLETED);
    }
  }

  public void createScheduleTasks(
      Long scheduleInstId, String orgSlug, List<TaskRequest> taskRequest, String bearerToken) {
    var scheduleInst = classroomScheduleInstRepository.findByIdAndOrgSlug(scheduleInstId, orgSlug);
    if (Objects.isNull(scheduleInst)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassroomScheduleValidity.Org",
          new String[] {orgSlug});
    }
    taskRequest.forEach(
        request -> {
          request.setClassroomScheduleInst(scheduleInst);
          if (request.getTaskType().equals(TaskType.REVISION.name())) {
            return;
          }
          if (Objects.equals(request.getQuestionsAssigneeMode(), QuestionsAssigneeMode.AUTO)
              && Objects.nonNull(request.getSubtopicDetail())
              && request.getTaskType().equals(TaskType.PRACTICE.name())) {
            request.setQuestionUuids(
                mlpService.getPracticeQuestionsUsingSubtopic(
                    request.getSubjectDetail().getSlug(),
                    request.getChapterDetail().getSlug(),
                    request.getSubtopicDetail().getSlug(),
                    request.getQuestionCount(),
                    bearerToken));
          }
          if (Objects.nonNull(request.getChapterDetail())
              && request.getTaskType().equals(TaskType.TEST.name())) {

            var questionResponse =
                contentService.getTestQuestionsByChapter(buildTestRequest(request), bearerToken);
            request.setQuestionUuids(
                new ArrayList<>(
                    questionResponse.getData().stream().map(Question::getUuid).toList()));
          }
        });
    scheduleInst.setTasks(taskService.createTaskByRequests(taskRequest, orgSlug));
    classroomScheduleInstRepository.save(scheduleInst);
  }

  private TestRequest buildTestRequest(TaskRequest request) {
    return TestRequest.builder()
        .chapters(List.of(request.getChapterDetail().getSlug()))
        .subject(request.getSubjectDetail().getId())
        .goalComplexity(1)
        .questionCount(request.getQuestionCount())
        .build();
  }

  public List<StudentScheduleResponse> getStudentSchedules(
      String orgSlug, Long studentId, Integer limit, Long epocDate) {
    var dt = LocalDate.now().toString();
    if (epocDate != null) {
      dt = String.valueOf(dateTimeUtil.convertEpochToIso8601Legacy(epocDate).toLocalDate());
    }
    return getStudentSchedulesByDate(orgSlug, dt, studentId, limit).stream().distinct().toList();
  }

  public List<StudentScheduleResponse> getStudentSchedulesByDate(
      String orgSlug, String date, Long studentId, Integer limit) {
    try {
      var studentSchedules =
          classroomScheduleInstRepository.getStudentSchedules(orgSlug, date, studentId, limit);
      if (studentSchedules.isEmpty()) {
        return Collections.emptyList();
      }
      return studentSchedules.stream()
          .map(
              schedule ->
                  StudentScheduleResponse.builder()
                      .classroomId(schedule.getClassroomId())
                      .classroomName(schedule.getClassroomName())
                      .displayName(schedule.getDisplayName())
                      .scheduleId(schedule.getScheduleId())
                      .scheduleInstId(schedule.getScheduleInstId())
                      .scheduleInstName(schedule.getScheduleInstName())
                      .dayOfWeek(schedule.getDayOfWeek())
                      .startTime(convertIso8601ToEpoch(schedule.getStartTime()))
                      .endTime(convertIso8601ToEpoch(schedule.getEndTime()))
                      .meetingId(schedule.getMeetingId())
                      .meetingName(schedule.getMeetingName())
                      .meetingLink(schedule.getMeetingLink())
                      .status(schedule.getStatus())
                      .attendance(
                          schedule.getAttendanceStatus() != null
                              ? schedule.getAttendanceStatus()
                              : ScheduleInstAttendanceStatus.NOT_MARKED.name())
                      .build())
          .toList();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentGet.Schedules", e);
    }
  }

  public TaskResponse getScheduleInstByscheduleInstsId(
      String orgSlug, Long scheduleInstId, Long fromDate, Long toDate, int limit) {
    return taskService.getTaskInstResponses(orgSlug, null, scheduleInstId, fromDate, toDate, limit);
  }

  public void migrateTutors() {
    try {
      var classroomScheduleInsts = classroomScheduleInstRepository.findAllByTutorsIsNull();
      if (classroomScheduleInsts.isEmpty()) {
        return;
      }
      classroomScheduleInsts.forEach(
          inst -> {
            try {
              inst.setTutors(
                  classroomScheduleService.getTutors(
                      inst.getClassroomSchedule().getClassroom().getTeachers()));
            } catch (Exception e) {
              log.error(
                  "error occurs while saving tutors for inst :%s".formatted(inst),
                  e.getMessage(),
                  e);
            }
          });
      classroomScheduleInstRepository.saveAll(new ArrayList<>(classroomScheduleInsts));
      log.debug("migration is done : " + classroomScheduleInsts.size());
    } catch (Exception exception) {
      log.error("error occurred while migrating tutors : ");
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public List<ClassroomScheduleInstResponse> getScheduleInsts(String orgSlug, long scheduleId) {
    var classroomSchedules =
        classroomScheduleRepository
            .findByIdAndOrgSlugAndDeletedAtIsNull(scheduleId, orgSlug)
            .orElseThrow();
    var activeInsts =
        classroomSchedules.getClassroomScheduleInsts().stream()
            .filter(inst -> LocalDateTime.now().isBefore(inst.getStartTime()))
            .sorted(Comparator.comparing(ClassroomScheduleInst::getStartTime).reversed())
            .toList();
    return sortedList(buildScheduleInstResponses(activeInsts));
  }
}
