package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeeGroup;
import com.wexl.erp.fees.model.FeeMaster;
import com.wexl.erp.fees.model.ScopeType;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeMasterRepository extends JpaRepository<FeeMaster, Long> {
  boolean existsByFeeGroupAndOrgSlugAndScopeType(
      FeeGroup feeGroup, String orgSlug, ScopeType scopeType);

  List<FeeMaster> findAllByOrgSlug(String orgSlug);

  List<FeeMaster> findAllByOrgSlugAndBoardSlug(String orgSlug, String boardSlug);

  Optional<FeeMaster> findByIdAndOrgSlug(UUID uuid, String orgSlug);
}
