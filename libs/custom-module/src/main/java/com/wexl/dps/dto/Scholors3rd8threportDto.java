package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record Scholors3rd8threportDto() {
  @Builder
  public record Model(Header header, Body body) {}

  @Builder
  public record Header(String schoolName, String address) {}

  @Builder
  public record Body(
      String name,
      String admissionNumber,
      String rollNo,
      String fatherName,
      String motherName,
      String sectionName,
      String height,
      String dateOfBirth,
      String weight,
      List<ScholasticMandatory> scholosticMandatory,
      String scholosticTotalPercentage,
      String scholosticTotalGrade,
      List<ScholasticOptional> scholosticOptional,
      String attendance,
      List<CoScholastic> coScholosticMandatory,
      List<CoScholastic> coScholosticOptional,
      String remarks,
      String issueDate) {}

  @Builder
  public record ScholasticMandatory(
      String subject,
      String pt,
      String ma,
      String portfolio,
      String se,
      String hye,
      double total,
      String grade) {}

  @Builder
  public record ScholasticOptional(
      String subject, String theory, String practical, double total, String grade) {}

  @Builder
  public record CoScholastic(String skillName, String term, List<Skill> skill) {}

  @Builder
  public record Skill(String subject, String grade) {}

  @Builder
  public record Marks(Double marksScored, Double totalMarks) {}
}
