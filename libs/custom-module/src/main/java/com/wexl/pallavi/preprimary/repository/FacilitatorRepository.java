package com.wexl.pallavi.preprimary.repository;

import com.wexl.pallavi.preprimary.model.Facilitators;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilitatorRepository extends JpaRepository<Facilitators, Long> {
    Facilitators findByOrgSlugAndGradeSlugAndSkill(
        String orgSlug, String gradeSlug, String skill);
}
