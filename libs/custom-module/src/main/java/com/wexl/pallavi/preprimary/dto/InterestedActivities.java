package com.wexl.pallavi.preprimary.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterestedActivities {

  @JsonProperty("reading")
  private Boolean reading;

  @JsonProperty("dancing")
  private Boolean dancing;

  @JsonProperty("singing")
  private Boolean singing;

  @JsonProperty("playing_musical_instrument")
  private Boolean playingMusicalInstrument;

  @JsonProperty("sport_or_games")
  private Boolean sportOrGames;

  @JsonProperty("creative_writing")
  private Boolean creativeWriting;

  @JsonProperty("gardening")
  private Boolean gardening;

  @JsonProperty("yoga")
  private Boolean yoga;

  @JsonProperty("art")
  private Boolean art;

  @JsonProperty("craft")
  private Boolean craft;

  @JsonProperty("cooking")
  private Boolean cooking;

  @JsonProperty("other")
  private Boolean other;

  @JsonProperty("other_specify")
  private String otherSpecify;
}
