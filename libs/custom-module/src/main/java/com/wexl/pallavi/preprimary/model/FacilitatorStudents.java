package com.wexl.pallavi.preprimary.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_pre_primary_facilitator_students")
public class FacilitatorStudents extends Model {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long studentId;

    private PeerAssessmentTypes term1;

    private PeerAssessmentTypes term2;

    @ManyToOne
    @JoinColumn(name = "pallavi_pre_primary_facilitator_id")
    private Facilitators facilitator;
}
